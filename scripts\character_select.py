from cProfile import label
import random
from PIL import Image
import gradio as gr
import modules.sd_samplers
import modules.scripts as scripts
from modules import shared
import json
import os
import shutil
import requests
import textwrap
from pprint import pprint
from modules.ui import gr_show
from collections import namedtuple
from pathlib import Path
from urllib.parse import urlparse
import time
import traceback


#  *********     versioning     *****
repo = "a1111"

try:
    import launch

    if not launch.is_installed("colorama"):
            launch.run_pip("install colorama")
except:
    pass

try:
    from colorama import just_fix_windows_console, Fore, Style
    just_fix_windows_console()
except:
    pass


class CharacterSelect(scripts.Script):

    BASEDIR = scripts.basedir()

    def __init__(self, *args, **kwargs):
        # components that pass through after_components
        self.all_components = []

        self.compinfo = namedtuple("CompInfo", ["component", "label", "elem_id", "kwargs"])

        self.settings_file = "settings.json"
        self.character_file = "character.json"
        self.action_file = "action.json"
        self.custom_settings_file = "custom_settings.json"
        self.custom_character_file = "custom_character.json"
        self.custom_action_file = "custom_action.json"

        # Read saved settings
        self.settings = self.get_config2(self.settings_file)
        self.character = self.get_config2(self.character_file)
        self.action = self.get_config2(self.action_file)

        self.copy_json_file(self.settings_file,self.custom_settings_file)
        self.copy_json_file(self.character_file,self.custom_character_file)
        self.copy_json_file(self.action_file,self.custom_action_file)

        try:
            self.settings = self.get_config2(self.custom_settings_file)
        except:
            print(f"错误：自订设定 '{self.custom_settings_file}' 不存在")

        try:
            self.character = self.get_config2(self.custom_character_file)
        except:
            print(f"错误：自订人物 '{self.custom_character_file}' 不存在")

        try:
            self.action = self.get_config2(self.custom_action_file)
        except:
            print(f"错误：自订动作 '{self.custom_action_file}' 不存在")

        #设定
        self.hm_config_1 = "custom_character.json"
        self.hm_config_2 = "custom_action.json"
        #self.hm_config_7 = "wai_character.json"
        #self.hm_config_8 = "wai_character2.json"

        #if(self.chk_character(self.hm_config_7) == False):
            #print("角色档1:" + self.settings["wai_json_url1"] + " 下载中")
            #self.download_json(self.settings["wai_json_url1"], os.path.join(CharacterSelect.BASEDIR, "wai_character.json"))
            #print("角色档1 下载完成")

        #if(self.chk_character(self.hm_config_8) == False):
        #    print("角色档2:" + self.settings["wai_json_url2"] + " 下载中")
        #    self.download_json(self.settings["wai_json_url2"], os.path.join(CharacterSelect.BASEDIR, "wai_character2.json"))
        #    print("角色档2 下载完成")

        self.hm_config_1_component = self.get_config2(self.hm_config_1)
        #for item in self.get_character(self.hm_config_7):
        #    self.hm_config_1_component.update({item : item})
        num_parts = 11
        self.hm_config_1_img = {}
        for i in range(num_parts):
            for item in self.get_config2(f"output_{i+1}.json"):
                key = list(item.keys())[0]
                self.hm_config_1_img[key] = item[key]
                self.hm_config_1_component.update({key : key})

        # No need to sort a dictionary, but we can ensure the component keys are sorted
        self.hm_config_1_component = {k: self.hm_config_1_component[k] for k in sorted(self.hm_config_1_component.keys())}

        self.hm_config_2_component = self.get_config2(self.hm_config_2)

        #self.hm_config_1_img = self.get_characterimg(self.hm_config_8)
        #for item in self.get_characterimg(self.hm_config_8):
        #    self.hm_config_1_img.append(item)

        self.localizations = "zh_CN.json"
        self.localizations_component = self.get_config2(self.localizations)
        self.relocalizations_component = {value: key for key, value in self.localizations_component.items()}

        self.hm1prompt = ""
        self.hm1promptary = []
        self.hm2prompt = ""
        self.hm2promptary = []

        #text value
        self.hm1btntext = ""
        self.hm2btntext = ""
        self.hm3btntext = ""
        self.hm4btntext = ""

        self.locked1 = ""
        self.locked2 = ""
        self.locked3 = ""
        self.locked4 = ""
        #loading - 避免回跳
        self.loading = 0

        #随机的face也要记下来 避免盖掉
        self.faceprompt = ""

        self.allfuncprompt = ""

        #前一次的 prompt
        self.oldAllPrompt=""

        #前一次的 cprompt
        self.oldcprompt=""

        self.elm_prfx = "characterselect"
        CharacterSelect.txt2img_neg_prompt_btn = gr.Button(
            value="使用预设值",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_neg_prompt_btn"
        )
        CharacterSelect.txt2img_prompt_btn = gr.Button(
            value="使用提词",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_prompt_btn"
        )
        CharacterSelect.txt2img_radom_C_prompt_btn = gr.Button(
            value="人物",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_C_randomprompt_btn",
            min_width=50
        )
        CharacterSelect.txt2img_radom_A_prompt_btn = gr.Button(
            value="动作",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_A_randomprompt_btn",
            min_width=50
        )
        CharacterSelect.txt2img_radom_prompt_btn = gr.Button(
            value="全部随机",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_randomprompt_btn",
            min_width=100
        )

        #h_m 人物
        CharacterSelect.txt2img_hm1_dropdown = gr.Dropdown(
            label="人物搜寻",
            choices=list(self.hm_config_1_component.keys()),
            render = False,
            elem_id=f"{self.elm_prfx}_hm1_dd"
        )

        CharacterSelect.txt2img_hm1_slider = gr.Slider(
            minimum = 0,
            maximum = len(self.hm_config_1_component) - 1,
            value = 0,
            step = 1,
            render = False,
            elem_id=f"{self.elm_prfx}_hm1_slider"
        )

        CharacterSelect.txt2img_hmzht_dropdown = gr.Dropdown(
            label="中文人物搜寻",
            choices=list(self.localizations_component.keys()),
            render = False,
            elem_id=f"{self.elm_prfx}_hmzht_dd"
        )

        CharacterSelect.txt2img_hm1_img = gr.Image(
            width = 100
        )

        #h_m 姿势
        CharacterSelect.txt2img_hm2_dropdown = gr.Dropdown(
            label="动作",
            choices=list(self.hm_config_2_component.keys()),
            render = False,
            elem_id=f"{self.elm_prfx}_hm2_dd"
        )

        #功能性调节
        CharacterSelect.func00_chk =gr.Checkbox(
            label="NSFW",
            render = False,
            container = False,
            elem_id=f"{self.elm_prfx}_func00_chk"
        )
        CharacterSelect.func01_chk =gr.Checkbox(
            label="增加细节",
            render = False,
            container = False,
            elem_id=f"{self.elm_prfx}_func01_chk"
        )
        CharacterSelect.func02_chk =gr.Checkbox(
            label="身材加强",
            render = False,
            container = False,
            elem_id=f"{self.elm_prfx}_func02_chk"
        )
        CharacterSelect.func03_chk = gr.Checkbox(
            label="品质加强",
            render = False,
            container = False,
            elem_id=f"{self.elm_prfx}_func03_chk"
        )
        CharacterSelect.func04_chk =gr.Checkbox(
            label="人物加强",
            render = False,
            container = False,
            elem_id=f"{self.elm_prfx}_func04_chk"
        )

        #锁定
        CharacterSelect.txt2img_lock1_btn = gr.Button(
            value="",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_lock1_btn"
        )
        CharacterSelect.txt2img_lock2_btn = gr.Button(
            value="",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_lock2_btn"
        )
        #中文输入框
        CharacterSelect.txt2img_cprompt_txt = gr.Textbox(lines=4, placeholder="可输入中文描述，透过AI扩充场景", label="AI 扩充 prompt", elem_id=f"{self.elm_prfx}_cprompt_txt")
        CharacterSelect.txt2img_cprompt_btn = gr.Button(
            value="AI扩充",
            label="cpromptbtn",
            variant="primary",
            render = False,
            elem_id=f"{self.elm_prfx}_cprompt_btn"
        )

        self.input_prompt = CharacterSelect.txt2img_cprompt_txt

    def fakeinit(self, *args, **kwargs):
        """
        __init__ workaround, since some data is not available during instantiation, such as is_img2img, filename, etc.
        This method is called from .show(), as that's the first method ScriptRunner calls after handing some state dat (is_txt2img, is_img2img2)
        """

        self.hide_all_button = gr.Button(value="简易版", variant="primary", render=False, visible=True, elem_id=f"{self.elm_prfx}_hide_all_bttn")
        self.show_all_button = gr.Button(value="一般版", variant="primary", render=False, visible=True, elem_id=f"{self.elm_prfx}_show_all_bttn")
        self.lock_seed_button = gr.Button(value="锁定seed", variant="primary", render=False, visible=True, elem_id=f"{self.elm_prfx}_lock_seed_bttn")
        self.rdn_seed_button = gr.Button(value="随机seed", variant="primary", render=False, visible=True, elem_id=f"{self.elm_prfx}_rdn_seed_bttn")

    def title(self):
        return "CharacterSelect"

    def before_component(self, component, **kwargs):
        pass
    def _before_component(self, component, **kwargs):
        # Define location of where to show up
        #if kwargs.get("elem_id") == "":#f"{'txt2img' if self.is_txt2img else 'img2img'}_progress_bar":
        #print(kwargs.get("label") == self.before_component_label, "TEST", kwargs.get("label"))
        #if kwargs.get("label") == self.before_component_label:
        # with gr.Row(equal_height = True):
        #     CharacterSelect.txt2img_neg_prompt_btn.render()
        with gr.Accordion(label="人物动作设定", open=True, elem_id=f"{'txt2img' if self.is_txt2img else 'img2img'}_preset_manager_accordion"):
            with gr.Row(equal_height=True):
                with gr.Column():
                    CharacterSelect.txt2img_hm1_dropdown.render()
                    CharacterSelect.txt2img_hmzht_dropdown.render()
                    CharacterSelect.txt2img_hm2_dropdown.render()
                    CharacterSelect.txt2img_hm1_slider.render()
                CharacterSelect.txt2img_hm1_img.render()

            with gr.Row(variant='compact'):
                CharacterSelect.txt2img_neg_prompt_btn.render()
                CharacterSelect.txt2img_radom_C_prompt_btn.render()
                CharacterSelect.txt2img_radom_A_prompt_btn.render()
                CharacterSelect.txt2img_radom_prompt_btn.render()
        with gr.Accordion(label="其他设定", open = False, elem_id=f"{'txt2img' if self.is_txt2img else 'img2img'}_h_setting_accordion"):
            with gr.Row(equal_height = True):
                CharacterSelect.func00_chk.render()
                CharacterSelect.func01_chk.render()
                CharacterSelect.func02_chk.render()
                CharacterSelect.func03_chk.render()
                CharacterSelect.func04_chk.render()
        if(self.settings["ai"]):
            with gr.Row(equal_height = True):
                CharacterSelect.txt2img_cprompt_txt.render()
                with gr.Row(equal_height = True):
                    CharacterSelect.txt2img_cprompt_btn.render()

    def after_component(self, component, **kwargs):
        if hasattr(component, "label") or hasattr(component, "elem_id"):
            self.all_components.append(self.compinfo(
                                                      component=component,
                                                      label=component.label if hasattr(component, "label") else None,
                                                      elem_id=component.elem_id if hasattr(component, "elem_id") else None,
                                                      kwargs=kwargs
                                                     )
                                      )

        label = kwargs.get("label")
        ele = kwargs.get("elem_id")

        # 提示词
        if ele == "txt2img_prompt":
            self.prompt_component = component
        if ele == "txt2img_neg_prompt":
            self.neg_prompt_component = component
        if ele == "txt2img_steps":
            self.steps_component = component
        if ele == "txt2img_height":
            self.height_component = component
        if ele == "txt2img_width":
            self.width_component = component


        if ele == "txt2img_generation_info_button" or ele == "img2img_generation_info_button":
            self._ui()

        if ele == "txt2img_styles_dialog":
            self._before_component("")

    def ui(self, *args):
        pass

    def _ui(self):
        # Conditional for class members
        if self.is_txt2img:
            #色色大师功能区
            CharacterSelect.txt2img_prompt_btn.click(
                fn=self.fetch_valid_values_from_prompt,
                outputs=self.prompt_component
            )
            CharacterSelect.txt2img_neg_prompt_btn.click(
                fn=self.fetch_neg_prompt,
                outputs=[self.neg_prompt_component,self.steps_component,self.height_component,self.width_component,self.prompt_component,self.func00_chk,self.func01_chk,self.func02_chk,self.func03_chk,self.func04_chk]
            )
            #hm
            CharacterSelect.txt2img_hm1_dropdown.change(
                fn=self.hm1_setting,
                inputs=[CharacterSelect.txt2img_hm1_dropdown,self.prompt_component],
                outputs=[CharacterSelect.txt2img_hm1_img, self.prompt_component,CharacterSelect.txt2img_hm1_slider,CharacterSelect.txt2img_hmzht_dropdown]
            )
            CharacterSelect.txt2img_hmzht_dropdown.change(
                fn=self.hmzht_setting,
                inputs=[CharacterSelect.txt2img_hmzht_dropdown],
                outputs=[CharacterSelect.txt2img_hm1_dropdown]
            )
            CharacterSelect.txt2img_hm1_slider.release(
                fn=self.hm1_setting2,
                inputs=[CharacterSelect.txt2img_hm1_slider,self.prompt_component],
                outputs=[CharacterSelect.txt2img_hm1_dropdown]
            )
            CharacterSelect.txt2img_hm2_dropdown.change(
                fn=self.hm2_setting,
                inputs=[CharacterSelect.txt2img_hm2_dropdown, self.prompt_component],
                outputs=[CharacterSelect.txt2img_hm2_dropdown, self.prompt_component]
            )

            #细节功能
            detailinput = [self.prompt_component,CharacterSelect.func00_chk,CharacterSelect.func01_chk,CharacterSelect.func02_chk,CharacterSelect.func03_chk,CharacterSelect.func04_chk]
            CharacterSelect.func00_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=self.prompt_component
            )
            CharacterSelect.func01_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=self.prompt_component
            )
            CharacterSelect.func02_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=self.prompt_component
            )
            CharacterSelect.func03_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=self.prompt_component
            )
            CharacterSelect.func04_chk.change(
                fn=self.func_setting,
                inputs=detailinput,
                outputs=self.prompt_component
            )
            #锁定
            #CharacterSelect.txt2img_lock1_btn.click(
            #    fn=self.prompt_lock1,
            #    outputs=[CharacterSelect.txt2img_hm1_dropdown, CharacterSelect.txt2img_lock1_btn]
            #)
            #CharacterSelect.txt2img_lock2_btn.click(
            #    fn=self.prompt_lock2,
            #    outputs=[CharacterSelect.txt2img_hm2_dropdown, CharacterSelect.txt2img_lock2_btn]
            #)
            CharacterSelect.txt2img_radom_C_prompt_btn.click(
                fn=self.h_m_random_C_prompt,
                inputs=self.prompt_component,
                outputs=[self.prompt_component, CharacterSelect.txt2img_hm1_dropdown]
            )
            CharacterSelect.txt2img_radom_A_prompt_btn.click(
                fn=self.h_m_random_A_prompt,
                inputs=self.prompt_component,
                outputs=[self.prompt_component,CharacterSelect.txt2img_hm2_dropdown]
            )
            CharacterSelect.txt2img_radom_prompt_btn.click(
                fn=self.h_m_random_prompt,
                inputs=self.prompt_component,
                outputs=[self.prompt_component, CharacterSelect.txt2img_hm1_dropdown,CharacterSelect.txt2img_hm2_dropdown]
            )
            CharacterSelect.txt2img_cprompt_btn.click(
                fn=self.cprompt_send,
                inputs=[self.prompt_component, self.input_prompt],
                outputs=self.prompt_component
            )


    def f_b_syncer(self):
        """
        ?Front/Backend synchronizer?
        Not knowing what else to call it, simple idea, rough to figure out. When updating choices on the front-end, back-end isn't updated, make them both match
        https://github.com/gradio-app/gradio/discussions/2848
        """
        self.inspect_dd.choices = [str(x) for x in self.all_components]
        return [gr.update(choices=[str(x) for x in self.all_components]), gr.Button.update(visible=False)]


    def inspection_formatter(self, x):
        comp = self.all_components[x]
        text = f"Component Label: {comp.label}\nElement ID: {comp.elem_id}\nComponent: {comp.component}\nAll Info Handed Down: {comp.kwargs}"
        return text


    def run(self, p, *args):
        pass

    def get_config(self, path, open_mode='r'):
        file = os.path.join(CharacterSelect.BASEDIR, path)
        try:
            with open(file, open_mode) as f:
                as_dict = json.load(f)
        except FileNotFoundError as e:
            print(f"{e}\n{file} not found, check if it exists or if you have moved it.")
        return as_dict

    def get_config2(self, path, open_mode='r'):
        file = os.path.join(CharacterSelect.BASEDIR, path)
        try:
            with open(file, open_mode, encoding='utf-8') as f:
                as_dict = json.load(f)
        except FileNotFoundError as e:
            print(f"{e}\n{file} not found, check if it exists or if you have moved it.")
        return as_dict

    def chk_character(self, path, open_mode='r'):
        file = os.path.join(CharacterSelect.BASEDIR, path)
        try:
            with open(file, open_mode) as f:
                return True
        except FileNotFoundError as e:
           return False

    def get_character(self, path, open_mode='r'):
        file = os.path.join(CharacterSelect.BASEDIR, path)
        try:
            with open(file, open_mode) as f:
                as_dict = json.load(f)
        except FileNotFoundError as e:
            print(f"{e}\n{file} not found, check if it exists or if you have moved it.")
        return [item["title"] for item in as_dict["proj"]]

    def get_characterimg(self, path, open_mode='r'):
        file = os.path.join(CharacterSelect.BASEDIR, path)
        try:
            with open(file, open_mode) as f:
                as_dict = json.load(f)
        except FileNotFoundError as e:
            print(f"{e}\n{file} not found, check if it exists or if you have moved it.")
        return [{item["title"]:item["image"]} for item in as_dict["proj"]]

    #自订提词
    def fetch_valid_values_from_prompt(self):
        self.prompt_component.value = ""
        self.prompt_component.value += self.hm1prompt
        self.prompt_component.value += self.hm2prompt
        self.prompt_component.value += self.allfuncprompt
        return self.prompt_component.value

    #预设
    def fetch_neg_prompt(self):
        self.neg_prompt_component.value = self.settings["neg_prompt"]
        self.steps_component.value = self.settings["steps"]
        self.height_component.value = self.settings["height"]
        self.width_component.value = self.settings["width"]

        self.allfuncprompt = ""
        self.allfuncprompt += self.settings["nsfw"]
        self.allfuncprompt += self.settings["more_detail"]
        self.allfuncprompt += self.settings["chihunhentai"]
        self.allfuncprompt += self.settings["quality"]
        self.allfuncprompt += self.settings["character_enhance"]

        return [self.neg_prompt_component.value,self.steps_component.value,self.height_component.value,self.width_component.value,self.allfuncprompt,True,True,True,True,True]


    #随机人
    def h_m_random_C_prompt(self,oldprompt):
        self.prompt_component.value = ""
        self.hm1btntext = list(self.hm_config_1_component)[random.randint(1,len(self.hm_config_1_component)-1)]
        self.prompt_component.value += self.hm_config_1_component[self.hm1btntext] + ","
        if(self.hm2btntext!=""):
            self.prompt_component.value += self.hm_config_2_component[self.hm2btntext] + ","
        self.prompt_component.value += self.allfuncprompt

        if(self.oldAllPrompt != ""):
            oldprompt = oldprompt.replace(", ",",").replace(self.oldAllPrompt.replace(", ",","), self.prompt_component.value)
        else:
            oldprompt = self.prompt_component.value

        self.oldAllPrompt = self.prompt_component.value

        return [oldprompt, self.hm1btntext]

    #随机
    def h_m_random_A_prompt(self,oldprompt):
        self.prompt_component.value = ""
        self.hm2btntext = list(self.hm_config_2_component)[random.randint(1,len(self.hm_config_2_component)-1)]
        if(self.hm1btntext!=""):
            self.prompt_component.value += self.hm_config_1_component[self.hm1btntext] + ","
        self.prompt_component.value += self.hm_config_2_component[self.hm2btntext] + ","
        self.prompt_component.value += self.allfuncprompt

        if(self.oldAllPrompt != ""):
            oldprompt = oldprompt.replace(", ",",").replace(self.oldAllPrompt.replace(", ",","), self.prompt_component.value)
        else:
            oldprompt = self.prompt_component.value

        self.oldAllPrompt = self.prompt_component.value

        return [oldprompt, self.hm2btntext]

    #随机
    def h_m_random_prompt(self, _):
        # oldprompt parameter is not used but required by the interface
        self.prompt_component.value = ""
        self.hm1btntext = list(self.hm_config_1_component)[random.randint(1,len(self.hm_config_1_component)-1)]
        self.hm2btntext = list(self.hm_config_2_component)[random.randint(1,len(self.hm_config_2_component)-1)]
        self.prompt_component.value += self.hm_config_1_component[self.hm1btntext] + ","
        self.prompt_component.value += self.hm_config_2_component[self.hm2btntext] + ","
        self.prompt_component.value += self.allfuncprompt

        self.oldAllPrompt = self.prompt_component.value

        return [self.prompt_component.value, self.hm1btntext, self.hm2btntext]

    #自订1
    async def hm1_setting(self, selection, oldprompt):
        if(self.loading>=1):
            return
        self.loading = 1
        try:
            if(selection == ""):
                selection = "random"
            oldhmprompt = self.hm1prompt.replace(",,",",").replace(", ",",")
            self.hm1prompt = ""
            # 移除未使用的变量 btntext
            #自行异动
            if(self.hm1btntext != selection):
                self.locked1 = ""
                if(selection != "random"):
                    self.hm1prompt = selection + ","
                    self.hm1btntext = selection
            else:
                if(selection != "random"):
                    self.hm1prompt = selection + ","

            #再次过滤
            self.hm1promptary.append(self.hm1prompt.replace(",,",","))
            for item in self.hm1promptary:
                if(item!=self.hm1prompt.replace(",,",",")):
                    oldprompt=oldprompt.replace(item.replace(",,",",").replace(", ",","),"")

            if(oldhmprompt!=""):
                if(oldprompt.replace(", ",",").find(oldhmprompt)>=0):
                    oldprompt = oldprompt.replace(", ",",").replace(oldhmprompt, self.hm1prompt)
                else:
                    if(oldprompt.replace(", ",",").find(self.hm1prompt.replace(", ",","))==-1):
                        oldprompt += "," + self.hm1prompt
            else:
                if(oldprompt.replace(", ",",").find(self.hm1prompt.replace(", ",","))==-1):
                    oldprompt += "," + self.hm1prompt

            # 初始化文件路径
            filepath = ""

            # 计算索引 - 找到选择的角色在排序后的键列表中的位置
            sorted_keys = sorted(self.hm_config_1_component.keys())
            try:
                index = sorted_keys.index(selection)
            except ValueError:
                index = 0  # 如果找不到，使用默认值

            # 直接从字典中获取图像路径
            if selection in self.hm_config_1_img:
                # 获取图像路径
                filepath = os.path.join(CharacterSelect.BASEDIR, self.hm_config_1_img[selection])

            #self.load_image_from_path(self.hm_config_1_img[0].get('hatsune miku'))

            # 加载图像
            try:
                image = self.load_image_from_path(filepath)
                return [image, oldprompt, index, self.relocalizations_component[self.hm1btntext]]
            except Exception as e:
                print(f"Error in hm1_setting when loading image: {e}")
                # 返回空白图像作为后备
                return [Image.new('RGB', (100, 100), color='gray'), oldprompt, index, self.relocalizations_component[self.hm1btntext]]
        except:
            traceback.print_exc()
            return
        finally:
            time.sleep(0.5)
            self.loading=0

    def hm1_setting2(self, selection, _):
        # oldprompt parameter is not used but required by the interface
        if(self.loading>=1):
            return
        self.loading=1
        try:
            return list(self.hm_config_1_component.keys())[selection]
        finally:
            time.sleep(0.5)
            self.loading=0

    def hmzht_setting(self, selection, _):
        # oldprompt parameter is not used but required by the interface
        if(self.loading>=1):
            return
        self.loading=1
        try:
            return self.localizations_component[selection]
        finally:
            time.sleep(0.5)
            self.loading=0


    #自订2
    def hm2_setting(self, selection, oldprompt):
        if(selection == ""):
            selection = "random"
        oldhmprompt = self.hm2prompt.replace(",,",",")
        self.hm2prompt = ""
        # 移除未使用的变量 btntext
        #自行异动
        if(self.hm2btntext != selection):
            self.locked2 = ""
            if(selection != "random"):
                self.hm2prompt = self.hm_config_2_component[selection] + ","
        else:
            if(selection != "random"):
                self.hm2prompt = self.hm_config_2_component[selection] + ","

        #再次过滤
        self.hm2promptary.append(self.hm2prompt.replace(",,",","))
        for item in self.hm2promptary:
            if(item!=self.hm2prompt.replace(",,",",")):
                oldprompt=oldprompt.replace(item.replace(",,",",").replace(", ",","),"")

        self.hm2btntext = selection
        if(oldhmprompt!=""):
            if(oldprompt.replace(", ",",").find(oldhmprompt.replace(", ",","))>=0):
                oldprompt = oldprompt.replace(", ",",").replace(oldhmprompt.replace(", ",","), self.hm2prompt)
            else:
                if(oldprompt.replace(", ",",").find(self.hm2prompt.replace(", ",","))==-1):
                    oldprompt += "," + self.hm2prompt
        else:
            if(oldprompt.replace(", ",",").find(self.hm2prompt.replace(", ",","))==-1):
                oldprompt += "," + self.hm2prompt

        return [selection, oldprompt]

    #细节
    def func_setting(self, oldprompt,fv0,fv1,fv2,fv3,fv4):
        oldprompt.replace(", ",",").replace(self.allfuncprompt.replace(", ",","), "")
        self.allfuncprompt = ""
        oldprompt = oldprompt.replace(self.settings["nsfw"], "")
        oldprompt = oldprompt.replace(self.settings["more_detail"], "")
        oldprompt = oldprompt.replace(self.settings["chihunhentai"], "")
        oldprompt = oldprompt.replace(self.settings["quality"], "")
        oldprompt = oldprompt.replace(self.settings["character_enhance"], "")
        if(fv0):
            self.allfuncprompt += self.settings["nsfw"]
        if(fv1):
            self.allfuncprompt += self.settings["more_detail"]
        if(fv2):
            self.allfuncprompt += self.settings["chihunhentai"]
        if(fv3):
            self.allfuncprompt += self.settings["quality"]
        if(fv4):
            self.allfuncprompt += self.settings["character_enhance"]
        oldprompt += self.allfuncprompt
        return oldprompt

    def prompt_lock1(self):
        if(self.locked1 == ""):
            self.locked1 = "Y"
            self.hm1prompt = self.hm1btntext
            try:
                btntext = "锁定:" + self.relocalizations_component[self.hm1btntext]
            except:
                btntext = "锁定:" + self.hm1btntext
        else:
            self.locked1 = ""
            try:
                btntext = self.relocalizations_component[self.hm1btntext]
            except:
                btntext = self.hm1btntext
            self.hm1prompt = ""
        return [self.hm1prompt, btntext]

    def prompt_lock2(self):
        if(self.locked2 == ""):
            self.locked2 = "Y"
            self.hm2prompt = self.hm2btntext
            try:
                btntext = "锁定:" + self.relocalizations_component[self.hm2btntext]
            except:
                btntext = "锁定:" + self.hm2btntext
        else:
            self.locked2 = ""
            btntext = self.hm2prompt
            self.hm2prompt = ""
        return [self.hm2prompt, btntext]

    def cprompt_send(self, oldprompt, input_prompt):
        generated_texts = []
        generated_texts = self.send_request(input_prompt)
        #clear beafore
        oldprompt = oldprompt.replace(self.oldcprompt, '')
        self.oldcprompt = ''
        for text in generated_texts:
            self.oldcprompt += text
        self.oldcprompt = self.oldcprompt.replace(", ", ",")
        oldprompt = oldprompt + ',' + self.oldcprompt
        print(f"llama3: {self.oldcprompt}")
        return oldprompt

    def send_request(self, input_prompt, **kwargs):
        prime_directive = textwrap.dedent("""\
            Act as a prompt maker with the following guidelines:
            - Break keywords by commas.
            - Provide high-quality, non-verbose, coherent, brief, concise, and not superfluous prompts.
            - Focus solely on the visual elements of the picture; avoid art commentaries or intentions.
            - Construct the prompt with the component format:
            1. Start with the subject and keyword description.
            2. Follow with motion keyword description.
            3. Follow with scene keyword description.
            4. Finish with background and keyword description.
            - Limit yourself to no more than 20 keywords per component
            - Include all the keywords from the user's request verbatim as the main subject of the response.
            - Be varied and creative.
            - Always reply on the same line and no more than 100 words long.
            - Do not enumerate or enunciate components.
            - Create creative additional information in the response.
            - Response in English.
            - Response prompt only.
            The followin is an illustartive example for you to see how to construct a prompt your prompts should follow this format but always coherent to the subject worldbuilding or setting and cosider the elemnts relationship.
            Example:
            Demon Hunter,Cyber City,A Demon Hunter,standing,lone figure,glow eyes,deep purple light,cybernetic exoskeleton,sleek,metallic,glowing blue accents,energy weapons,Fighting Demon,grotesque creature,twisted metal,glowing red eyes,sharp claws,towering structures,shrouded haze,shimmering energy,
            Make a prompt for the following Subject:
            """)
        data = {
                'model': self.settings["model"],
                'messages': [
                    {"role": "system", "content": prime_directive},
                    {"role": "user", "content": input_prompt + ";Response in English"}
                ],
            }
        headers = kwargs.get('headers', {"Content-Type": "application/json", "Authorization": "Bearer " + self.settings["api_key"]})
        base_url = self.settings["base_url"]
        response = requests.post(base_url, headers=headers, json=data)

        if response.status_code == 200:
            return response.json().get('choices', [{}])[0].get('message', {}).get('content', '')
        else:
            print(f"Error: Request failed with status code {response.status_code}")
            return []

    def local_request_restart(self):
        "Restart button"
        shared.state.interrupt()
        shared.state.need_restart = True

    def load_image_from_path(self, image_path):
        """从文件加载图像"""
        try:
            return Image.open(image_path)
        except Exception as e:
            print(f"Error loading image: {e}")
            return Image.new('RGB', (100, 100), color='gray')

    def copy_json_file(self, source_path: str, destination_path: str, overwrite: bool = False):
        """
        复制JSON档案并确认其格式正确
        Parameters:
        source_path (str): 来源JSON档案的路径
        destination_path (str): 目标位置的路径
        overwrite (bool): 若为True则覆写已存在的档案，预设为False
        Returns:
        bool: 复制成功返回True，失败返回False
        """
        try:
            # 确认来源档案存在
            file = Path(os.path.join(CharacterSelect.BASEDIR, source_path))
            if not file.exists():
                print(f"错误：来源档案 '{source_path}' 不存在")
                return False

            # 检查目标档案是否已存在
            dest = Path(os.path.join(CharacterSelect.BASEDIR, destination_path))
            if dest.exists() and not overwrite:
                return False

            # 读取并验证JSON格式
            #with open(file, 'r', encoding='utf-8') as file:
            #    json.load(file)  # 确认JSON格式正确

            # 建立目标资料夹（如果不存在）
            #dest.parent.mkdir(parents=True, exist_ok=True)

            # 复制档案
            shutil.copy2(os.path.join(CharacterSelect.BASEDIR, source_path), os.path.join(CharacterSelect.BASEDIR, destination_path))
            print(f"成功：档案已复制到 '{dest}'")
            return True

        except json.JSONDecodeError:
            print(f"错误：'{source_path}' 不是有效的JSON档案")
            return False
        except Exception as e:
            print(f"错误：复制过程发生问题 - {str(e)}")
            return False

    def download_json(self, url, output_path, timeout:int=600):
        """
        从网址下载 JSON 档案并储存

        Parameters:
        url (str): JSON 档案的网址
        output_path (str): 储存档案的路径
        headers (dict): 自订的 HTTP headers
        timeout (int): 请求超时时间（秒）

        Returns:
        tuple: (下载的数据, 储存路径)
        """
        try:
            # 设定预设 headers
            default_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            # 发送请求
            response = requests.get(url, headers=default_headers, timeout=timeout)
            response.raise_for_status()  # 检查是否请求成功

            # 尝试解析 JSON
            data = response.json()

            # 储存档案
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except requests.exceptions.RequestException as e:
            print(f"下载发生错误: {str(e)}")
            raise
        except json.JSONDecodeError as e:
            print(f"JSON 解析错误: {str(e)}")
            raise
        except Exception as e:
            print(f"发生未预期的错误: {str(e)}")
            raise


